import type { ModelApi } from '@/api/apiService'
import type { QueryParams } from '@/types/api/queryParams'
import type { GridOptions } from '../types'
import { ref, type Ref } from 'vue'
import { performanceMonitor } from './performanceMonitor'

// 防抖控制器映射
const debounceControllers = new Map<
  string,
  { timer: number; controller: AbortController }
>()

async function getDataGridData(
  gridOptions: Ref<GridOptions>,
  modelApi: ModelApi,
  customQueryParams?: QueryParams
) {
  const componentId = modelApi.moduleModel || 'unknown'
  
  // 检查是否启用性能监控
  const isPerformanceTraceEnabled = gridOptions.value.tracePerformance === true

  // 开始性能监控
  const startTime = performance.now()
  if (isPerformanceTraceEnabled) {
    performanceMonitor.startComponentMonitoring(componentId)
  }

  // 如果提供了自定义查询参数，使用它；否则使用 gridOptions 中的参数
  const queryParams = customQueryParams || {
    offset: 0,
    limit: 20,
    ...gridOptions.value.toolbarOptions?.queryParams,
  }

  // ====== 关键逻辑：自动去除空条件 filters 字段 ======
  if (
    queryParams.filters &&
    Array.isArray(queryParams.filters.conditions) &&
    queryParams.filters.conditions.length === 0
  ) {
    delete queryParams.filters
  }
  // ====== END ======

  // 取消之前的请求（如果存在）
  const existingDebounce = debounceControllers.get(componentId)
  if (existingDebounce) {
    clearTimeout(existingDebounce.timer)
    existingDebounce.controller.abort()
  }

  // 创建新的取消控制器
  const abortController = new AbortController()

  try {
    console.log(`📊 [DataGrid ${componentId}] 开始数据请求`, queryParams)

    // 添加取消信号到请求中（如果 modelApi 支持）
    const queryData = await modelApi.getList(queryParams)

    const loadTime = performance.now() - startTime
    console.log(
      `⚡ [DataGrid ${componentId}] 数据加载完成，耗时: ${loadTime.toFixed(2)}ms`
    )

    // 确保数据存在且格式正确
    if (queryData && queryData.total >= 0 && Array.isArray(queryData.items)) {
      // 创建新数组引用以确保Vue响应式系统检测到变化
      gridOptions.value.data = [...queryData.items]
      gridOptions.value.toolbarOptions.total = queryData.total

      // 记录组件数据大小到性能监控器
      const rows = queryData.items.length
      const cols = gridOptions.value.columns?.length || 0
      if (isPerformanceTraceEnabled) {
        performanceMonitor.recordComponentDataSize(componentId, rows, cols)
        performanceMonitor.recordDataLoadTime(componentId, loadTime)
      }

      // 更新 gridOptions 中的 queryParams，确保组件状态同步
      if (customQueryParams) {
        gridOptions.value.toolbarOptions.queryParams = {
          ...gridOptions.value.toolbarOptions.queryParams,
          ...customQueryParams,
        }
      }

      console.log(
        `✅ [DataGrid ${componentId}] 数据渲染就绪: ${rows}行 × ${cols}列`
      )
    } else {
      console.warn(`⚠️ [DataGrid ${componentId}] 数据格式异常`, queryData)
      gridOptions.value.toolbarOptions.total = 0
    }
  } catch (error) {
    const loadTime = performance.now() - startTime

    // 如果是主动取消的请求，不记录为错误
    if (abortController.signal.aborted) {
      console.log(`🚫 [DataGrid ${componentId}] 请求已取消`)
      return
    }

    console.error(
      `❌ [DataGrid ${componentId}] 获取数据失败 (${loadTime.toFixed(2)}ms):`,
      error
    )
    gridOptions.value.data = []

    // 重置 total 为 0
    if (gridOptions.value.toolbarOptions?.total) {
      const total = gridOptions.value.toolbarOptions.total || 0
      if (typeof total === 'object' && total !== null) {
        ;(total as { value: number }).value = 0
      } else {
        gridOptions.value.toolbarOptions.total = 0
      }
    }

    // 记录错误到性能监控
    if (isPerformanceTraceEnabled) {
      performanceMonitor.recordDataLoadTime(componentId, loadTime)
    }
    throw error
  } finally {
    // 清理防抖控制器
    debounceControllers.delete(componentId)
  }
}

// 清理所有防抖控制器的工具函数
export function clearAllDebounceControllers() {
  debounceControllers.forEach(({ timer, controller }) => {
    clearTimeout(timer)
    controller.abort()
  })
  debounceControllers.clear()
}

export const dataHelpers = {
  getDataGridData,
  clearAllDebounceControllers,
}
