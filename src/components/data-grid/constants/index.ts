import { ref } from 'vue'
import type { GridOptions } from '../types'

/**
 * 默认的表格配置
 */
export const DEFAULT_GRID_OPTIONS: GridOptions = {
  enableSelection: true,
  border: false,
  stripe: true,
  height: 'auto',
  minHeight: '200px',
  maxHeight: '800px',
  size: 'small',
  loading: false, // 默认不显示 loading
  loadingConfig: {
    type: 'spinner',
    text: '数据加载中...',
    size: 'medium',
    color: 'primary',
    theme: 'auto',
    overlay: {
      opacity: 0.8,
      blur: true,
    },
    animation: {
      speed: 'normal',
    },
  },
  toolbarOptions: {
    tableActions: {
      refresh: true,
      fullScreen: true,
      gridSetting: true,
      dataExport: false,
    },
    queryParams: {
      offset: 0,
      limit: 30,
    },
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  showOverflow: false, // 禁用省略号，允许内容撑开行高
  showHeaderOverflow: 'ellipsis',
  showFooterOverflow: 'ellipsis',
  scrollY: {
    enabled: true,
  },
  scrollX: {
    enabled: true,
  },
  footerConfig: {
    enabled: false,
    position: 'bottom',
    sticky: false,
    showBorder: true,
    summaries: {},
  },
  virtualYConfig: {
    enabled: true,
    gt: 50,
  },
  virtualXConfig: {
    enabled: true,
    gt: 20,
  },
  columnConfig: {
    resizable: true,
    drag: true,
    minWidth: 60,
    // autoOptions: {
    //   isCalcBody: true
    // }
  },
  columnDragConfig: {
    showIcon: false,
    trigger: 'cell',
  },
}
